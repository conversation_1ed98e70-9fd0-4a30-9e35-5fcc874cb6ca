/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpSvrMgr.cpp

Abstract:

    Implementation of the MCP Server Manager component that manages and enumerates
    registered MCP servers.
    
Important Note: 
        
    Windows MCP (WMCP), which is based on the WinRT IModelContextProtocolServerCatalog APIs, is currently 
    not enabled in AIMX because the feature is still under development.

    For reference, we can find a preview implementation in the Os.2020\rs_winpd_uxip branch under:
    SDXROOT\onecoreuap\shell\CoreSettingHandlers\StorageSense.

    This implementation uses the exact same JSON-RPC interface defined in the official specification:
    https://modelcontextprotocol.io/specification/2025-06-18

    This interface is designed to support communication between both in-process (MCPServer) and 
    out-of-process (MCPServer) components.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/11/2025

Revision History:

    1. <PERSON><PERSON><PERSON> (rizhang) 07/11/2025 
    - Initial implementation

    2. <PERSON><PERSON><PERSON> (rizhang) 07/13/2025 
    - Added support for in-process MCP servers

    3. <PERSON><PERSON><PERSON> (rizhang) 07/19/2025 
    - Added support for JSON-based MCP server that allows customer to add their own servers 
      by adding entries to the aimx_mcpsvr.json file.

    Note: 

    A)  The JSON-based MCP servers configured below are all counted as out-of-process servers in AIMXSrv.
        The file is by default located at C:\ProgramData\Microsoft\AIMX\aimx_mcpsvr.json, but can be overridden
        by setting the registry value: HKLM\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters\McpConfigPath

    B)  AIMXSrv is not responsible for the MCP Server runtime dependency management. The customer is responsible
        for ensuring that the server program is runable and properly configured on the system.

    A sample entry looks as below:

      {
        "mcpServers": 
        {
          "OOP-helloMcpServer": 
          {
            "enabled": true,
            "type": "stdio",
            "command": "D:\\os\\bin\\amd64fre\\aimx\\helloMcpServer.exe",
            "args": [],
            "description": "Hello MCP Server - a sample configuration-based MCP server for AIMX system",
            "version": "1.0.0",
            "workingDirectory": "",
            "env": 
            {
              "MCP_SERVER_MODE": "stdio"
            }
          }
        }
      }


--*/

#include "pch.hxx"
#include "McpSvrMgr.h"
#include "McpStdioClient.h"
#include <fstream>
#include <shlobj_core.h>
#include <shlwapi.h>
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpSvrMgr.cpp.tmh"
#include "McpStdioClient.h"
#include "StringUtils.h"
#include "IInProcessMcpServer.h"

#include <cpprest/http_client.h>
#include "RagServiceManager.h"

// Static member definitions
McpSvrMgr* McpSvrMgr::s_instance = nullptr;
std::mutex McpSvrMgr::s_instanceMutex;

// Configuration-based server storage
std::unordered_map<std::wstring, MCP_SERVER_CONFIG> McpSvrMgr::s_configuredServers;
std::shared_mutex McpSvrMgr::s_configuredServersMutex;

// Simple connection pool
std::unordered_map<std::wstring, std::shared_ptr<McpStdioClient>> McpSvrMgr::s_connectionPool;
std::mutex McpSvrMgr::s_connectionPoolMutex;

HRESULT
McpSvrMgr::Initialize()
/*++

Routine Description:
    Initialize the MCP Server Manager component. 

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);

    TraceInfo(AimxMcpSvrMgr, "Initializing MCP Server Manager");

    if (s_instance != nullptr)
    {
        TraceInfo(AimxMcpSvrMgr, "MCP Server Manager already initialized");
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }

    s_instance = new (std::nothrow) McpSvrMgr();
    if (s_instance == nullptr)
    {
        TraceErr(AimxMcpSvrMgr, "Failed to allocate MCP Server Manager instance");
        return E_OUTOFMEMORY;
    }

    s_instance->m_initialized = true;

    // Load configured servers
    std::wstring configPath = GetConfigurationPath();
    HRESULT hr = LoadConfiguredServers(configPath);
    if (FAILED(hr))
    {
        TraceWarn(AimxMcpSvrMgr, "No Json-based MCP servers configured, continuing..");        
    }

    TraceInfo(AimxMcpSvrMgr, "MCP Server Manager initialized successfully");
    return S_OK;
}

void
McpSvrMgr::Uninitialize()
/*++

Routine Description:
    Uninitialize the MCP Server Manager and cleanup all resources.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);

    TraceInfo(AimxMcpSvrMgr, "Uninitializing MCP Server Manager");

    if (s_instance != nullptr)
    {
        // Clearservers
        {
            std::unique_lock<std::shared_mutex> serverLock(s_instance->m_serverMapMutex);            
            s_instance->m_serverInfos.clear();
        }


        delete s_instance;
        s_instance = nullptr;
    }

    TraceInfo(AimxMcpSvrMgr, "MCP Server Manager uninitialized");
}


HRESULT
McpSvrMgr::GetEnabledMcpServers(
    _Out_ std::vector<MCP_SERVER_INFO>& servers
    )
/*++

Routine Description:
    Get all enabled MCP servers from the cached server information.

Arguments:
    servers - Vector to receive enabled server information

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Getting enabled MCP servers (both out-of-process and in-process)");

    servers.clear();

    // Get enabled out-of-process servers
    {
        std::shared_lock<std::shared_mutex> lock(s_instance->m_serverMapMutex);
        for (const auto& serverPair : s_instance->m_serverInfos)
        {
            const MCP_SERVER_INFO& serverInfo = serverPair.second;

            if (serverInfo.isEnabled)
            {
                servers.push_back(serverInfo);
            }
        }
    }

    // In-process servers are now stored in the unified m_serverInfos map
    // No separate enumeration needed

    TraceInfo(AimxMcpSvrMgr, "Found %d enabled servers (unified storage)",
              static_cast<int>(servers.size()));

    return S_OK;
}

HRESULT
McpSvrMgr::GetServerInfoByName(
    _In_ const std::wstring& serverName,
    _Out_ MCP_SERVER_INFO& serverInfo
    )
/*++

Routine Description:
    Get server information by server name.

Arguments:
    serverName - Name of the server to find
    serverInfo - Output server information

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::shared_lock<std::shared_mutex> lock(s_instance->m_serverMapMutex);

    TraceInfo(AimxMcpSvrMgr, "Looking for server by name: %ws", serverName.c_str());

    // Debug: List all available servers
    TraceInfo(AimxMcpSvrMgr, "Available servers in m_serverInfos: %d", static_cast<int>(s_instance->m_serverInfos.size()));
    for (const auto& serverPair : s_instance->m_serverInfos)
    {
        const MCP_SERVER_INFO& currentServerInfo = serverPair.second;
        TraceInfo(AimxMcpSvrMgr, "  Server: '%ws' (Type: %d, Status: %d)",
                 currentServerInfo.serverName.c_str(),
                 static_cast<int>(currentServerInfo.serverType),
                 static_cast<int>(currentServerInfo.status));
    }

    // Debug: List all in-process servers
    {
        std::shared_lock<std::shared_mutex> inProcessLock(s_instance->m_inProcessServersMutex);
        TraceInfo(AimxMcpSvrMgr, "Available in-process servers: %d", static_cast<int>(s_instance->m_inProcessServers.size()));
        for (const auto& inProcessPair : s_instance->m_inProcessServers)
        {
            TraceInfo(AimxMcpSvrMgr, "  In-process server: '%ws'", inProcessPair.first.c_str());
        }
    }

    // Search through all servers to find one with matching name
    for (const auto& serverPair : s_instance->m_serverInfos)
    {
        const MCP_SERVER_INFO& currentServerInfo = serverPair.second;

        // Compare server names (case-insensitive)
        if (_wcsicmp(currentServerInfo.serverName.c_str(), serverName.c_str()) == 0)
        {
            serverInfo = currentServerInfo;
            TraceInfo(AimxMcpSvrMgr, "Found server: %ws (ID: %ws)",
                     serverInfo.serverName.c_str(),
                     GuidToString(serverInfo.serverId).c_str());
            return S_OK;
        }
    }

    // In-process servers are now stored in the unified m_serverInfos map
    // No separate lookup needed

    // Try configuration-based servers
    {
        std::shared_lock<std::shared_mutex> configLock(s_configuredServersMutex);
        auto it = s_configuredServers.find(serverName);
        if (it != s_configuredServers.end())
        {
            TraceInfo(AimxMcpSvrMgr, "Found configured server: %ws", serverName.c_str());
            return CreateServerInfoFromConfig(it->second, serverInfo);
        }
    }

    TraceWarn(AimxMcpSvrMgr, "Server not found: %ws", serverName.c_str());
    return E_FAIL;
}

HRESULT
McpSvrMgr::ExecuteServerTool(
    _In_ const GUID& serverId,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Execute a tool on a specific MCP server using the MCP protocol.

Arguments:
    serverId - GUID of the server containing the tool
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    result - Output result from tool execution

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Executing tool %ws on server %ws",
             toolName.c_str(), GuidToString(serverId).c_str());

    try
    {
        // Get server info to determine server type
        MCP_SERVER_INFO serverInfo;
        HRESULT hr = s_instance->GetServerInfoInternal(serverId, serverInfo);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to get server info for tool execution: %!HRESULT!", hr);
            return hr;
        }

        // Route based on server type
        if (serverInfo.serverType == MCP_SERVER_TYPE::IN_PROCESS)
        {
            return s_instance->ExecuteInProcessServerTool(serverInfo, toolName, parameters, result);
        }
        else
        {
            return s_instance->ExecuteOutOfProcessServerTool(serverId, toolName, parameters, result);
        }

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception executing server tool: %s", e.what());
        result = nlohmann::json::object();
        result["status"] = "error";
        result["message"] = e.what();
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxMcpSvrMgr, "Unknown exception executing server tool");
        result = nlohmann::json::object();
        result["status"] = "error";
        result["message"] = "Unknown exception occurred";
        return E_FAIL;
    }
}


McpSvrMgr*
McpSvrMgr::GetInstance()
/*++

Routine Description:
    Get the singleton instance of the MCP Server Manager.

Arguments:
    None.

Return Value:
    Pointer to the singleton instance, or nullptr if not initialized.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    return s_instance;
}

// Private constructor
McpSvrMgr::McpSvrMgr()
    : m_initialized(false)    
{
    TraceInfo(AimxMcpSvrMgr, "MCP Server Manager instance created");
}

// Private destructor
McpSvrMgr::~McpSvrMgr()
{
    TraceInfo(AimxMcpSvrMgr, "MCP Server Manager instance destroyed");
}

HRESULT
McpSvrMgr::GetServerInfoInternal(
    _In_ const GUID& serverId,
    _Out_ MCP_SERVER_INFO& serverInfo
    )
/*++

Routine Description:
    Internal method to get server information by ID.

Arguments:
    serverId - GUID of the server
    serverInfo - Output server information

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    std::shared_lock<std::shared_mutex> lock(m_serverMapMutex);

    auto it = m_serverInfos.find(serverId);
    if (it != m_serverInfos.end())
    {
        serverInfo = it->second;
        return S_OK;
    }

    return E_FAIL;
}

HRESULT
McpSvrMgr::ExecuteInProcessServerTool(
    _In_ const MCP_SERVER_INFO& serverInfo,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Execute a tool on an in-process MCP server using direct API calls.

Arguments:
    serverInfo - Server information containing the in-process server instance
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    result - Output result from tool execution

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AimxMcpSvrMgr, "Executing in-process tool: %ws", toolName.c_str());

    try
    {
        if (serverInfo.serverType != MCP_SERVER_TYPE::IN_PROCESS ||
            !serverInfo.serverInstance)
        {
            TraceErr(AimxMcpSvrMgr, "Invalid in-process server info");
            return E_INVALIDARG;
        }

        auto server = serverInfo.serverInstance;

        // Create MCP tools/call request JSON using shared library
        std::string toolNameUtf8 = WideToUtf8(toolName);
        nlohmann::json callRequest = McpProtocol::Mcp::CreateCallToolRequest(toolNameUtf8, parameters);

        // Execute tool via direct API call
        nlohmann::json callResponse;
        HRESULT hr = server->CallTool(callRequest, callResponse);

        if (SUCCEEDED(hr))
        {
            // Extract result from MCP response
            if (callResponse.contains("result"))
            {
                result = callResponse["result"];
            }
            else
            {
                result = callResponse;
            }

            TraceInfo(AimxMcpSvrMgr, "In-process tool execution completed successfully");
        }
        else
        {
            TraceErr(AimxMcpSvrMgr, "In-process tool execution failed: %!HRESULT!", hr);
            result = callResponse; // May contain error information
        }

        return hr;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception executing in-process tool: %s", e.what());
        result = nlohmann::json::object();
        result["error"] = e.what();
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::ExecuteOutOfProcessServerTool(
    _In_ const GUID& serverId,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Execute a tool on an out-of-process MCP server using stdio client.
    Enhanced to support both WinRT servers and configuration-based servers.

Arguments:
    serverId - GUID of the server containing the tool
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    result - Output result from tool execution

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AimxMcpSvrMgr, "Executing out-of-process tool: %ws", toolName.c_str());

    try
    {
        // Get server info to determine if this is a configured server
        MCP_SERVER_INFO serverInfo;
        HRESULT hr = s_instance->GetServerInfoInternal(serverId, serverInfo);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to get server info for tool execution: %!HRESULT!", hr);
            return hr;
        }

        std::wstring serverName = serverInfo.serverName.c_str();
        std::string toolNameStr = WideToUtf8(toolName);

        // Check if this is a configuration-based server
        MCP_SERVER_CONFIG config;
        bool isConfiguredServer = false;

        {
            std::shared_lock<std::shared_mutex> lock(s_configuredServersMutex);
            auto it = s_configuredServers.find(serverName);
            if (it != s_configuredServers.end())
            {
                config = it->second;
                isConfiguredServer = true;
            }
        }

        if (isConfiguredServer)
        {
            // Use connection pool for configured servers
            std::shared_ptr<McpStdioClient> client;
            hr = GetOrCreateConnection(serverName, config, client);
            if (FAILED(hr))
            {
                TraceErr(AimxMcpSvrMgr, "Failed to get connection for configured server: %!HRESULT!", hr);
                result = nlohmann::json::object();
                result["status"] = "connection_failed";
                result["message"] = "Failed to get connection for configured server";
                result["tool"] = toolNameStr;
                return hr;
            }

            // Default timeout of 30 seconds
            DWORD timeoutMs = 30000;

            hr = client->CallToolWithTimeout(toolNameStr, parameters, timeoutMs, result);
            if (FAILED(hr))
            {
                TraceErr(AimxMcpSvrMgr, "Failed to execute configured tool '%s': %!HRESULT!",
                         toolNameStr.c_str(), hr);

                // Remove unhealthy connection from pool
                std::lock_guard<std::mutex> lock(s_connectionPoolMutex);
                s_connectionPool.erase(serverName);

                if (result.empty())
                {
                    result = nlohmann::json::object();
                    result["status"] = "execution_failed";
                    result["message"] = "Tool execution failed";
                    result["tool"] = toolNameStr;
                }
            }

            TraceInfo(AimxMcpSvrMgr, "Successfully executed configured tool '%s'", toolNameStr.c_str());
            return hr;
        }
        else
        {
            // Note: This is currently disabled due to platform support issues
            TraceWarn(AimxMcpSvrMgr, "other type of Servers as WinRT server execution not supported on this platform");
            result = nlohmann::json::object();
            result["status"] = "not_supported";
            result["message"] = "WinRT server execution not supported on this platform";
            result["tool"] = toolNameStr;
            return E_NOTIMPL;
        }
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception executing out-of-process tool: %s", e.what());
        result = nlohmann::json::object();
        result["status"] = "error";
        result["message"] = e.what();
        return E_FAIL;
    }

}

HRESULT
McpSvrMgr::RegisterInProcessServer(
    _In_ std::shared_ptr<IInProcessMcpServer> server
    )
/*++

Routine Description:
    Register an in-process MCP server.

Arguments:
    server - Shared pointer to the in-process server

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    if (!server)
    {
        return E_INVALIDARG;
    }

    std::wstring serverName = server->GetServerName();
    TraceInfo(AimxMcpSvrMgr, "Registering in-process MCP server: %ws", serverName.c_str());

    try
    {
        // Initialize the server
        HRESULT hr = server->Initialize();
        if (FAILED(hr))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to initialize in-process server: %!HRESULT!", hr);
            return hr;
        }

        // Generate a GUID for the in-process server
        GUID serverId;
        hr = ::CoCreateGuid(&serverId);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to generate GUID for in-process server: %!HRESULT!", hr);
            return hr;
        }

        // Create server info for the main server map
        MCP_SERVER_INFO serverInfo;
        serverInfo.serverId = GUID(serverId);
        serverInfo.serverName = serverName;
        serverInfo.description = server->GetServerDescription();
        serverInfo.packageFullName = L""; 
        serverInfo.serverType = MCP_SERVER_TYPE::IN_PROCESS;
        serverInfo.status = MCP_SERVER_STATUS::RUNNING;
        serverInfo.isEnabled = true;
        serverInfo.serverInstance = server;

        // Get tools from the in-process server
        try
        {
            nlohmann::json toolsResponse;
            HRESULT toolsHr = server->ListTools(toolsResponse);
            if (SUCCEEDED(toolsHr) && toolsResponse.contains(AimxConstants::Protocol::AIMX_FIELD_RESULT) &&
                toolsResponse[AimxConstants::Protocol::AIMX_FIELD_RESULT].contains(AimxConstants::Protocol::AIMX_FIELD_TOOLS))
            {
                for (const auto& toolJson : toolsResponse[AimxConstants::Protocol::AIMX_FIELD_RESULT][AimxConstants::Protocol::AIMX_FIELD_TOOLS])
                {
                    MCP_TOOL_INFO toolInfo;
                    toolInfo.serverName = serverName;
                    toolInfo.toolName = Utf8ToWide(toolJson.value(AimxConstants::Protocol::AIMX_FIELD_NAME, ""));
                    toolInfo.description = Utf8ToWide(toolJson.value(AimxConstants::Protocol::AIMX_FIELD_DESCRIPTION, ""));
                    toolInfo.isAvailable = true;

                    if (toolJson.contains(AimxConstants::Protocol::AIMX_FIELD_INPUT_SCHEMA))
                    {
                        toolInfo.inputSchema = toolJson[AimxConstants::Protocol::AIMX_FIELD_INPUT_SCHEMA];
                    }

                    serverInfo.availableTools.push_back(toolInfo);
                }
            }
        }
        catch (...)
        {
            TraceWarn(AimxMcpSvrMgr, "Failed to get tools from in-process server during registration: %ws", serverName.c_str());
        }

        // Register the server in both maps
        {
            std::unique_lock<std::shared_mutex> lock(s_instance->m_inProcessServersMutex);
            s_instance->m_inProcessServers[serverName] = server;
        }

        {
            std::unique_lock<std::shared_mutex> lock(s_instance->m_serverMapMutex);
            s_instance->m_serverInfos[serverInfo.serverId] = serverInfo;
        }

        TraceInfo(AimxMcpSvrMgr, "Successfully registered in-process MCP server: %ws with ID: %ws",
                 serverName.c_str(), GuidToString(serverInfo.serverId).c_str());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception registering in-process server: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::UnregisterInProcessServer(
    _In_ const std::wstring& serverName
    )
/*++

Routine Description:
    Unregister an in-process MCP server.

Arguments:
    serverName - Name of the server to unregister

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Unregistering in-process MCP server: %ws", serverName.c_str());

    std::unique_lock<std::shared_mutex> lock(s_instance->m_inProcessServersMutex);

    auto it = s_instance->m_inProcessServers.find(serverName);
    if (it != s_instance->m_inProcessServers.end())
    {
        // Uninitialize the server
        it->second->Uninitialize();

        // Remove from registry
        s_instance->m_inProcessServers.erase(it);

        TraceInfo(AimxMcpSvrMgr, "Successfully unregistered in-process MCP server: %ws", serverName.c_str());
        return S_OK;
    }

    TraceWarn(AimxMcpSvrMgr, "In-process server not found: %ws", serverName.c_str());
    return E_FAIL;
}

HRESULT
McpSvrMgr::RegisterInProcessServerFactory(
    _In_ std::shared_ptr<IInProcessMcpServerFactory> factory
    )
/*++

Routine Description:
    Register an in-process MCP server factory.

Arguments:
    factory - Factory for creating server instances

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    if (!factory)
    {
        return E_INVALIDARG;
    }

    std::wstring serverType = factory->GetServerType();
    TraceInfo(AimxMcpSvrMgr, "Registering in-process MCP server factory: %ws", serverType.c_str());

    try
    {
        std::unique_lock<std::shared_mutex> lock(s_instance->m_inProcessServersMutex);
        s_instance->m_inProcessFactories.push_back(factory);

        TraceInfo(AimxMcpSvrMgr, "Successfully registered in-process MCP server factory: %ws", serverType.c_str());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception registering in-process server factory: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::LoadConfiguredServers(
    _In_ const std::wstring& configPath
    )
/*++

Routine Description:
    Load configuration-based MCP servers from a JSON configuration file.

Arguments:
    configPath - Path to the configuration file (empty for default path)

Return Value:
    S_OK on success, S_FALSE if no config file, error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::wstring actualConfigPath = configPath.empty() ? GetConfigurationPath() : configPath;

    TraceInfo(AimxMcpSvrMgr, "Loading configured servers from: %ws", actualConfigPath.c_str());

    try
    {
        // Load JSON configuration
        std::ifstream configFile(actualConfigPath);
        if (!configFile.is_open())
        {
            TraceWarn(AimxMcpSvrMgr, "Configuration file not found: %ws", actualConfigPath.c_str());

            // Create default configuration file
            HRESULT hr = CreateDefaultConfigurationFile(actualConfigPath);
            if (FAILED(hr))
            {
                TraceErr(AimxMcpSvrMgr, "Failed to create default configuration file: %!HRESULT!", hr);
                return S_FALSE; 
            }

            TraceInfo(AimxMcpSvrMgr, "Created default configuration file, no servers configured");
            return S_OK;
        }

        nlohmann::json config;
        configFile >> config;

        if (!config.contains("mcpServers"))
        {
            TraceInfo(AimxMcpSvrMgr, "No MCP servers found in the configuration file");
            return S_OK;
        }

        std::lock_guard<std::shared_mutex> lock(s_configuredServersMutex);
        s_configuredServers.clear();

        // Parse server configurations
        for (const auto& [serverName, serverConfig] : config["mcpServers"].items())
        {
            MCP_SERVER_CONFIG mcpConfig = {};
            mcpConfig.serverName = Utf8ToWide(serverName);
            mcpConfig.enabled = serverConfig.value("enabled", true);

            if (!mcpConfig.enabled)
            {
                TraceInfo(AimxMcpSvrMgr, "Skipping disabled server: %ws", mcpConfig.serverName.c_str());
                continue;
            }

            std::string serverType = serverConfig.value("type", "stdio");
            if (serverType == "stdio")
            {
                mcpConfig.serverType = MCP_SERVER_TYPE::OUT_OF_PROCESS;
                mcpConfig.command = Utf8ToWide(serverConfig.value("command", ""));
                mcpConfig.description = Utf8ToWide(serverConfig.value("description", ""));
                mcpConfig.version = Utf8ToWide(serverConfig.value("version", "1.0.0"));

                if (serverConfig.contains("args"))
                {
                    for (const auto& arg : serverConfig["args"])
                    {
                        mcpConfig.arguments.push_back(Utf8ToWide(arg.get<std::string>()));
                    }
                }

                if (serverConfig.contains("env"))
                {
                    for (const auto& [key, value] : serverConfig["env"].items())
                    {
                        mcpConfig.environment[key] = value.get<std::string>();
                    }
                }

                mcpConfig.workingDirectory = Utf8ToWide(serverConfig.value("workingDirectory", ""));

                s_configuredServers[mcpConfig.serverName] = mcpConfig;

                TraceInfo(AimxMcpSvrMgr, "Loaded configured server: %ws (command: %ws)",
                         mcpConfig.serverName.c_str(), mcpConfig.command.c_str());
            }
            else
            {
                TraceWarn(AimxMcpSvrMgr, "Unsupported server type '%s' for server '%ws'",
                         serverType.c_str(), mcpConfig.serverName.c_str());
            }
        }

        // Add configured servers to unified storage for enumeration
        {
            std::unique_lock<std::shared_mutex> unifiedLock(s_instance->m_serverMapMutex);
            for (const auto& [serverName, config] : s_configuredServers)
            {
                MCP_SERVER_INFO serverInfo;
                HRESULT hr = CreateServerInfoFromConfig(config, serverInfo);
                if (SUCCEEDED(hr))
                {
                    // Check if server already exists in unified storage
                    bool alreadyExists = false;
                    for (const auto& [existingId, existingInfo] : s_instance->m_serverInfos)
                    {
                        if (existingInfo.serverName == serverInfo.serverName)
                        {
                            alreadyExists = true;
                            break;
                        }
                    }

                    if (!alreadyExists)
                    {
                        s_instance->m_serverInfos[serverInfo.serverId] = serverInfo;
                        TraceInfo(AimxMcpSvrMgr, "Added configured server to unified storage: %ws",
                                 serverInfo.serverName.c_str());
                    }
                }
                else
                {
                    TraceWarn(AimxMcpSvrMgr, "Failed to create server info for configured server: %ws",
                             config.serverName.c_str());
                }
            }
        }

        TraceInfo(AimxMcpSvrMgr, "Loaded %lu configured servers", (unsigned long)s_configuredServers.size());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Failed to load configuration: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::CreateServerInfoFromConfig(
    _In_ const MCP_SERVER_CONFIG& config,
    _Out_ MCP_SERVER_INFO& serverInfo
    )
/*++

Routine Description:
    Create a MCP_SERVER_INFO structure from a configuration.

Arguments:
    config - Server configuration
    serverInfo - Output server information

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    try
    {
        // Generate a unique GUID for this configured server
        GUID serverId;
        if (FAILED(CoCreateGuid(&serverId)))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to create GUID for configured server");
            return E_FAIL;
        }

        // Fill in server info
        serverInfo.serverId = GUID(serverId);
        serverInfo.serverName = config.serverName;
        serverInfo.description = config.description;
        serverInfo.packageFullName = L""; // Empty for configured servers
        serverInfo.serverType = config.serverType;
        serverInfo.status = config.enabled ? MCP_SERVER_STATUS::ENABLED : MCP_SERVER_STATUS::DISABLED;
        serverInfo.isEnabled = config.enabled;

        // Store command and arguments for out-of-process servers
        if (config.serverType == MCP_SERVER_TYPE::OUT_OF_PROCESS)
        {
            serverInfo.command = config.command;
            serverInfo.arguments = config.arguments;
        }

        // Clear in-process server instance (not applicable for configured servers)
        serverInfo.serverInstance = nullptr;

        // Populate availableTools by actually connecting to the configured server
        if (config.serverType == MCP_SERVER_TYPE::OUT_OF_PROCESS && config.enabled)
        {
            TraceInfo(AimxMcpSvrMgr, "Connecting to configured server to get tools: %ws", config.serverName.c_str());

            std::shared_ptr<McpStdioClient> client;
            HRESULT hr = GetOrCreateConnection(config.serverName, config, client);
            if (SUCCEEDED(hr) && client)
            {
                hr = client->ListTools(serverInfo.availableTools);
                if (SUCCEEDED(hr))
                {
                    TraceInfo(AimxMcpSvrMgr, "Successfully populated %d tools for configured server: %ws",
                             static_cast<int>(serverInfo.availableTools.size()), config.serverName.c_str());
                }
                else
                {
                    TraceWarn(AimxMcpSvrMgr, "Failed to get tools from configured server: %ws, hr: %!HRESULT!",
                             config.serverName.c_str(), hr);
                    serverInfo.availableTools.clear(); // Clear on failure
                }
            }
            else
            {
                TraceWarn(AimxMcpSvrMgr, "Failed to connect to configured server: %ws, hr: %!HRESULT!",
                         config.serverName.c_str(), hr);
                serverInfo.availableTools.clear(); // Clear on failure
            }
        }
        else
        {
            // For disabled servers or in-process servers, clear tools
            serverInfo.availableTools.clear();
        }

        TraceInfo(AimxMcpSvrMgr, "Created server info from config: %ws with %d tools",
                 config.serverName.c_str(), static_cast<int>(serverInfo.availableTools.size()));
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception creating server info from config: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::GetOrCreateConnection(
    _In_ const std::wstring& serverName,
    _In_ const MCP_SERVER_CONFIG& config,
    _Out_ std::shared_ptr<McpStdioClient>& client
    )
/*++

Routine Description:
    Get an existing healthy connection or create a new one for a configured server.

Arguments:
    serverName - Name of the server
    config - Server configuration
    client - Output client connection

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_connectionPoolMutex);

    TraceInfo(AimxMcpSvrMgr, "Getting or creating connection for server: %ws", serverName.c_str());

    // Check if we have an existing healthy connection
    auto it = s_connectionPool.find(serverName);
    if (it != s_connectionPool.end())
    {
        auto existingClient = it->second;
        if (existingClient->IsConnected() && existingClient->IsHealthy())
        {
            TraceInfo(AimxMcpSvrMgr, "Reusing existing healthy connection for server: %ws", serverName.c_str());
            client = existingClient;
            return S_OK;
        }
        else
        {
            TraceWarn(AimxMcpSvrMgr, "Removing unhealthy connection for server: %ws", serverName.c_str());
            // Remove unhealthy connection
            s_connectionPool.erase(it);
        }
    }

    // Create new connection
    std::string serverNameUtf8 = WideToUtf8(serverName);
    auto newClient = std::make_shared<McpStdioClient>(serverNameUtf8);

    std::string commandUtf8 = WideToUtf8(config.command);
    std::vector<std::string> argsUtf8;
    for (const auto& arg : config.arguments)
    {
        argsUtf8.push_back(WideToUtf8(arg));
    }

    std::string workingDirUtf8 = WideToUtf8(config.workingDirectory);

    TraceInfo(AimxMcpSvrMgr, "Creating new connection for server: %ws (command: %ws)",
             serverName.c_str(), config.command.c_str());

    HRESULT hr = newClient->ConnectPersistent(commandUtf8, argsUtf8,
                                             config.environment, workingDirUtf8);
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to create connection for server: %ws, hr: %!HRESULT!",
                 serverName.c_str(), hr);
        return hr;
    }

    // Cache the connection
    s_connectionPool[serverName] = newClient;
    client = newClient;

    TraceInfo(AimxMcpSvrMgr, "Successfully created and cached connection for server: %ws", serverName.c_str());
    return S_OK;
}

void
McpSvrMgr::CleanupUnhealthyConnections()
/*++

Routine Description:
    Clean up unhealthy connections from the connection pool.

--*/
{
    std::lock_guard<std::mutex> lock(s_connectionPoolMutex);

    auto it = s_connectionPool.begin();
    while (it != s_connectionPool.end())
    {
        auto& [serverName, client] = *it;

        if (!client->IsConnected() || !client->IsHealthy())
        {
            TraceInfo(AimxMcpSvrMgr, "Removing unhealthy connection for server: %ws", serverName.c_str());
            it = s_connectionPool.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

std::wstring
McpSvrMgr::GetConfigurationPath()
/*++

Routine Description:
    Get the configuration file path from registry or use default.

Return Value:
    Configuration file path.

--*/
{
    // Check registry for custom path
    HKEY hKey;
    LONG result = RegOpenKeyExW(HKEY_LOCAL_MACHINE,
                               AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS,
                               0, KEY_READ, &hKey);
    if (result == ERROR_SUCCESS)
    {
        wchar_t configPath[MAX_PATH];
        DWORD dataSize = sizeof(configPath);
        result = RegQueryValueExW(hKey, AimxConstants::McpConfiguration::AIMX_MCP_CONFIG_REGISTRY_KEY,
                                 nullptr, nullptr,
                                 reinterpret_cast<BYTE*>(configPath), &dataSize);
        RegCloseKey(hKey);

        if (result == ERROR_SUCCESS)
        {
            TraceInfo(AimxMcpSvrMgr, "Using registry-configured path: %ws", configPath);
            return std::wstring(configPath);
        }
    }

    // Default path
    wchar_t programData[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(nullptr, CSIDL_COMMON_APPDATA, nullptr, 0, programData)))
    {
        std::wstring defaultPath = std::wstring(programData) +
                                  AimxConstants::McpConfiguration::AIMX_MCP_CONFIG_DIRECTORY_PATH +
                                  AimxConstants::McpConfiguration::AIMX_MCP_DEFAULT_CONFIG_FILENAME;
        TraceInfo(AimxMcpSvrMgr, "Using default configuration path: %ws", defaultPath.c_str());
        return defaultPath;
    }

    // Fallback path
    std::wstring fallbackPath = std::wstring(AimxConstants::McpConfiguration::AIMX_MCP_CONFIG_FALLBACK_BASE_PATH) +
                               AimxConstants::McpConfiguration::AIMX_MCP_CONFIG_DIRECTORY_PATH +
                               AimxConstants::McpConfiguration::AIMX_MCP_DEFAULT_CONFIG_FILENAME;
    TraceWarn(AimxMcpSvrMgr, "Using fallback configuration path: %ws", fallbackPath.c_str());
    return fallbackPath;
}

HRESULT
McpSvrMgr::CreateDefaultConfigurationFile(
    _In_ const std::wstring& configPath
    )
/*++

Routine Description:
    Create a default empty MCP configuration file with the basic structure.

Arguments:
    configPath - Path where the configuration file should be created

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpSvrMgr, "Creating default configuration file: %ws", configPath.c_str());

    try
    {
        // Extract directory path from the full file path
        std::wstring directoryPath = configPath;
        size_t lastSlash = directoryPath.find_last_of(L'\\');
        if (lastSlash != std::wstring::npos)
        {
            directoryPath = directoryPath.substr(0, lastSlash);
        }

        // Create directory structure if it doesn't exist
        if (!CreateDirectoryW(directoryPath.c_str(), nullptr))
        {
            DWORD error = GetLastError();
            if (error != ERROR_ALREADY_EXISTS)
            {
                TraceErr(AimxMcpSvrMgr, "Failed to create directory: %ws, error: %lu",
                        directoryPath.c_str(), error);
                return HRESULT_FROM_WIN32(error);
            }
        }

        // Create the default JSON structure
        nlohmann::json defaultConfig = nlohmann::json::object();
        defaultConfig["mcpServers"] = nlohmann::json::object();

        // Write the configuration file
        std::ofstream configFile(configPath);
        if (!configFile.is_open())
        {
            TraceErr(AimxMcpSvrMgr, "Failed to create configuration file: %ws", configPath.c_str());
            return E_FAIL;
        }

        // Json with 2-space indentation
        configFile << defaultConfig.dump(2);
        configFile.close();

        TraceInfo(AimxMcpSvrMgr, "Successfully created default configuration file: %ws", configPath.c_str());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception creating default configuration file: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
McpSvrMgr::RegisterConfiguredServer(
    _In_ const MCP_SERVER_CONFIG& config
    )
/*++

Routine Description:
    Register a new configured server at runtime.

Arguments:
    config - Server configuration to register

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Registering configured server: %ws", config.serverName.c_str());

    // Add to configured servers storage
    {
        std::lock_guard<std::shared_mutex> lock(s_configuredServersMutex);
        s_configuredServers[config.serverName] = config;
    }

    // Add to unified storage for enumeration
    {
        std::unique_lock<std::shared_mutex> unifiedLock(s_instance->m_serverMapMutex);

        MCP_SERVER_INFO serverInfo;
        HRESULT hr = CreateServerInfoFromConfig(config, serverInfo);
        if (SUCCEEDED(hr))
        {
            // Check if server already exists in unified storage
            bool alreadyExists = false;
            for (const auto& [existingId, existingInfo] : s_instance->m_serverInfos)
            {
                if (existingInfo.serverName == serverInfo.serverName)
                {
                    // Update existing entry
                    s_instance->m_serverInfos[existingId] = serverInfo;
                    alreadyExists = true;
                    TraceInfo(AimxMcpSvrMgr, "Updated existing server in unified storage: %ws",
                             serverInfo.serverName.c_str());
                    break;
                }
            }

            if (!alreadyExists)
            {
                s_instance->m_serverInfos[serverInfo.serverId] = serverInfo;
                TraceInfo(AimxMcpSvrMgr, "Added new configured server to unified storage: %ws",
                         serverInfo.serverName.c_str());
            }
        }
        else
        {
            TraceWarn(AimxMcpSvrMgr, "Failed to create server info for configured server: %ws",
                     config.serverName.c_str());
        }
    }

    TraceInfo(AimxMcpSvrMgr, "Successfully registered configured server: %ws", config.serverName.c_str());
    return S_OK;
}

HRESULT
McpSvrMgr::UnregisterConfiguredServer(
    _In_ const std::wstring& serverName
    )
/*++

Routine Description:
    Unregister a configured server and clean up its connection.

Arguments:
    serverName - Name of the server to unregister

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Unregistering configured server: %ws", serverName.c_str());

    // Remove from configured servers
    {
        std::lock_guard<std::shared_mutex> lock(s_configuredServersMutex);
        auto it = s_configuredServers.find(serverName);
        if (it != s_configuredServers.end())
        {
            s_configuredServers.erase(it);
        }
    }

    // Remove from unified storage
    {
        std::unique_lock<std::shared_mutex> unifiedLock(s_instance->m_serverMapMutex);
        auto it = s_instance->m_serverInfos.begin();
        while (it != s_instance->m_serverInfos.end())
        {
            if (it->second.serverName == serverName)
            {
                TraceInfo(AimxMcpSvrMgr, "Removing configured server from unified storage: %ws", serverName.c_str());
                it = s_instance->m_serverInfos.erase(it);
                break;
            }
            else
            {
                ++it;
            }
        }
    }

    // Remove from connection pool
    {
        std::lock_guard<std::mutex> lock(s_connectionPoolMutex);
        auto it = s_connectionPool.find(serverName);
        if (it != s_connectionPool.end())
        {
            s_connectionPool.erase(it);
        }
    }

    TraceInfo(AimxMcpSvrMgr, "Successfully unregistered configured server: %ws", serverName.c_str());
    return S_OK;
}

HRESULT
McpSvrMgr::GetConfiguredServers(
    _Out_ std::vector<std::wstring>& serverNames
    )
/*++

Routine Description:
    Get the list of all configured server names.

Arguments:
    serverNames - Output vector of server names

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::shared_lock<std::shared_mutex> lock(s_configuredServersMutex);

    serverNames.clear();
    serverNames.reserve(s_configuredServers.size());

    for (const auto& [serverName, config] : s_configuredServers)
    {
        serverNames.push_back(serverName);
    }

    TraceInfo(AimxMcpSvrMgr, "Retrieved %lu configured server names", (unsigned long)serverNames.size());
    return S_OK;
}

HRESULT
McpSvrMgr::GetServerConfiguration(
    _In_ const std::wstring& serverName,
    _Out_ MCP_SERVER_CONFIG& config
    )
/*++

Routine Description:
    Get the configuration for a specific configured server.

Arguments:
    serverName - Name of the server
    config - Output server configuration

Return Value:
    S_OK on success, error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::shared_lock<std::shared_mutex> lock(s_configuredServersMutex);

    auto it = s_configuredServers.find(serverName);
    if (it == s_configuredServers.end())
    {
        TraceWarn(AimxMcpSvrMgr, "Configured server not found: %ws", serverName.c_str());
        return E_FAIL;
    }

    config = it->second;
    TraceInfo(AimxMcpSvrMgr, "Retrieved configuration for server: %ws", serverName.c_str());
    return S_OK;
}

HRESULT
McpSvrMgr::RegisterAllToolsWithRagService()
/*++

Routine Description:
    Register all MCP tools with the RAG service for semantic search.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxMcpSvrMgr, "Registering all MCP tools with RAG service");

    try
    {
        std::vector<MCP_SERVER_INFO> enabledServers;
        HRESULT hr = GetEnabledMcpServers(enabledServers);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpSvrMgr, "Failed to get enabled MCP servers: %!HRESULT!", hr);
            return hr;
        }

        int totalToolsRegistered = 0;
        int totalServersRegistered = 0;

        for (const auto& server : enabledServers)
        {
            if (!server.isEnabled || server.availableTools.empty())
            {
                continue;
            }

            // Register the server first
            hr = RagServiceManager::RegisterServer(
                server.serverId,
                server.serverName,
                server.description,
                L"1.0.0"
            );
            if (FAILED(hr))
            {
                TraceWarn(AimxMcpSvrMgr, "Failed to register server %ws with RAG service: %!HRESULT!",
                         server.serverName.c_str(), hr);
                // Continue with tool registration even if server registration fails
            }
            else
            {
                totalServersRegistered++;
                TraceInfo(AimxMcpSvrMgr, "Registered server %ws with RAG service", server.serverName.c_str());
            }

            // Register all tools from this server
            for (const auto& tool : server.availableTools)
            {
                hr = RagServiceManager::RegisterTool(tool, server.serverId);
                if (FAILED(hr))
                {
                    TraceWarn(AimxMcpSvrMgr, "Failed to register tool %ws from server %ws with RAG service: %!HRESULT!",
                             tool.toolName.c_str(), server.serverName.c_str(), hr);
                    // Continue with next tool
                }
                else
                {
                    totalToolsRegistered++;
                    TraceInfo(AimxMcpSvrMgr, "Registered tool %ws from server %ws with RAG service",
                             tool.toolName.c_str(), server.serverName.c_str());
                }
            }
        }

        TraceInfo(AimxMcpSvrMgr, "RAG service registration completed: %d servers, %d tools registered",
                 totalServersRegistered, totalToolsRegistered);
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Exception during RAG service registration: %s", e.what());
        return E_FAIL;
    }
}