using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace netRag;

/// <summary>
/// Service for managing MCP servers
/// </summary>
public class McpServerService
{
    private readonly ILogger<McpServerService> _logger;
    private readonly ConcurrentDictionary<string, McpServer> _servers;
    private readonly object _lockObject = new();

    public McpServerService(ILogger<McpServerService> logger)
    {
        _logger = logger;
        _servers = new ConcurrentDictionary<string, McpServer>();
    }

    /// <summary>
    /// Register a new MCP server
    /// </summary>
    public Task<bool> RegisterServerAsync(RegisterServerRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(request.Name))
            {
                _logger.LogWarning("Server ID and Name are required for registration");
                return Task.FromResult(false);
            }

            // Validate GUID format
            if (!Guid.TryParse(request.Id, out _))
            {
                _logger.LogWarning("Invalid server ID format: {ServerId}. Expected GUID format", request.Id);
                return Task.FromResult(false);
            }

            var server = new McpServer
            {
                Id = request.Id,
                Name = request.Name,
                Description = request.Description,
                Version = request.Version,
                RegisteredAt = DateTime.UtcNow,
                ToolCount = 0,
                Metadata = request.Metadata
            };

            _servers.AddOrUpdate(request.Id, server, (key, oldValue) => server);

            _logger.LogInformation("MCP server registered successfully: {ServerId} ({ServerName})",
                server.Id, server.Name);

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register MCP server: {ServerId}", request.Id);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Get a specific MCP server by ID
    /// </summary>
    public Task<McpServer?> GetServerAsync(string serverId)
    {
        _servers.TryGetValue(serverId, out var server);
        return Task.FromResult(server);
    }

    /// <summary>
    /// Get all registered MCP servers
    /// </summary>
    public Task<List<McpServer>> GetAllServersAsync()
    {
        return Task.FromResult(_servers.Values.ToList());
    }

    /// <summary>
    /// Update tool count for a server
    /// </summary>
    public Task UpdateServerToolCountAsync(string serverId, int toolCount)
    {
        if (_servers.TryGetValue(serverId, out var server))
        {
            server.ToolCount = toolCount;
            _logger.LogDebug("Updated tool count for server {ServerId}: {ToolCount}", serverId, toolCount);
        }
        return Task.CompletedTask;
    }

    /// <summary>
    /// Remove a server and all its tools
    /// </summary>
    public Task<bool> RemoveServerAsync(string serverId)
    {
        var removed = _servers.TryRemove(serverId, out var server);
        if (removed && server != null)
        {
            _logger.LogInformation("MCP server removed: {ServerId} ({ServerName})", 
                server.Id, server.Name);
        }
        return Task.FromResult(removed);
    }

    /// <summary>
    /// Get server statistics
    /// </summary>
    public Task<Dictionary<string, object>> GetServerStatisticsAsync()
    {
        var servers = _servers.Values.ToList();
        var totalTools = servers.Sum(s => s.ToolCount);
        
        var stats = new Dictionary<string, object>
        {
            ["totalServers"] = servers.Count,
            ["totalTools"] = totalTools,
            ["averageToolsPerServer"] = servers.Count > 0 ? (double)totalTools / servers.Count : 0,
            ["servers"] = servers.Select(s => new
            {
                id = s.Id,
                name = s.Name,
                toolCount = s.ToolCount,
                registeredAt = s.RegisteredAt,
                version = s.Version
            }).ToList()
        };

        return Task.FromResult(stats);
    }

    /// <summary>
    /// Check if a server exists
    /// </summary>
    public Task<bool> ServerExistsAsync(string serverId)
    {
        return Task.FromResult(_servers.ContainsKey(serverId));
    }
}
