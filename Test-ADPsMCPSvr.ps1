#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test script for ADPsMCPSvr MCP server commands
    
.DESCRIPTION
    This script tests various Active Directory PowerShell commands through the MCP server
    to identify which commands are hanging or causing issues. It includes timeout handling
    and detailed logging to help troubleshoot PowerShell execution problems.
    
.PARAMETER ServerPath
    Path to the ADPsMCPSvr server script (default: auto-detect)
    
.PARAMETER TimeoutSeconds
    Timeout for each command test in seconds (default: 30)
    
.PARAMETER TestBasicOnly
    Only test basic commands (skip complex operations)
    
.PARAMETER LogFile
    Path to log file for detailed output (default: Test-ADPsMCPSvr.log)
    
.EXAMPLE
    .\Test-ADPsMCPSvr.ps1
    
.EXAMPLE
    .\Test-ADPsMCPSvr.ps1 -TimeoutSeconds 60 -TestBasicOnly
    
.NOTES
    Requires:
    - Windows PowerShell 5.1 or PowerShell 7+
    - Active Directory PowerShell module
    - Domain-joined machine or appropriate AD credentials
#>

[CmdletBinding()]
param(
    [string]$ServerPath = "",
    [int]$TimeoutSeconds = 30,
    [switch]$TestBasicOnly,
    [string]$LogFile = "Test-ADPsMCPSvr.log"
)

# Initialize logging
$LogPath = Join-Path $PWD $LogFile
$StartTime = Get-Date

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogPath -Value $logEntry
}

function Test-MCPCommand {
    param(
        [string]$CommandName,
        [hashtable]$Parameters = @{},
        [int]$TimeoutSec = 30
    )
    
    Write-Log "Testing command: $CommandName" "TEST"
    
    try {
        # Create JSON-RPC request
        $request = @{
            jsonrpc = "2.0"
            id = [System.Guid]::NewGuid().ToString()
            method = "tools/call"
            params = @{
                name = $CommandName
                arguments = $Parameters
            }
        } | ConvertTo-Json -Depth 5
        
        Write-Log "Request: $request" "DEBUG"
        
        # Start the MCP server process with timeout
        $processStartInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processStartInfo.FileName = "powershell.exe"
        $processStartInfo.Arguments = "-ExecutionPolicy Bypass -NoProfile -File `"$ServerPath`""
        $processStartInfo.UseShellExecute = $false
        $processStartInfo.RedirectStandardInput = $true
        $processStartInfo.RedirectStandardOutput = $true
        $processStartInfo.RedirectStandardError = $true
        $processStartInfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processStartInfo
        
        # Event handlers for async output reading
        $outputBuilder = New-Object System.Text.StringBuilder
        $errorBuilder = New-Object System.Text.StringBuilder
        
        $outputHandler = {
            if (-not [string]::IsNullOrEmpty($Event.SourceEventArgs.Data)) {
                [void]$outputBuilder.AppendLine($Event.SourceEventArgs.Data)
            }
        }
        
        $errorHandler = {
            if (-not [string]::IsNullOrEmpty($Event.SourceEventArgs.Data)) {
                [void]$errorBuilder.AppendLine($Event.SourceEventArgs.Data)
            }
        }
        
        Register-ObjectEvent -InputObject $process -EventName OutputDataReceived -Action $outputHandler | Out-Null
        Register-ObjectEvent -InputObject $process -EventName ErrorDataReceived -Action $errorHandler | Out-Null
        
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        # Send initialization request first
        $initRequest = @{
            jsonrpc = "2.0"
            id = 1
            method = "initialize"
            params = @{
                protocolVersion = "2024-11-05"
                capabilities = @{
                    tools = @{}
                }
                clientInfo = @{
                    name = "Test-ADPsMCPSvr"
                    version = "1.0.0"
                }
            }
        } | ConvertTo-Json -Depth 5
        
        $process.StandardInput.WriteLine($initRequest)
        $process.StandardInput.Flush()
        
        # Wait a moment for initialization
        Start-Sleep -Milliseconds 500
        
        # Send the actual command
        $process.StandardInput.WriteLine($request)
        $process.StandardInput.Flush()
        $process.StandardInput.Close()
        
        # Wait for completion with timeout
        $completed = $process.WaitForExit($TimeoutSec * 1000)
        
        if (-not $completed) {
            Write-Log "Command timed out after $TimeoutSec seconds" "ERROR"
            $process.Kill()
            $process.WaitForExit(5000)
            return @{
                Success = $false
                Error = "Timeout after $TimeoutSec seconds"
                Output = $outputBuilder.ToString()
                ErrorOutput = $errorBuilder.ToString()
            }
        }
        
        # Clean up event handlers
        Get-EventSubscriber | Where-Object { $_.SourceObject -eq $process } | Unregister-Event
        
        $output = $outputBuilder.ToString()
        $errorOutput = $errorBuilder.ToString()
        
        Write-Log "Command completed. Exit code: $($process.ExitCode)" "DEBUG"
        Write-Log "Output: $output" "DEBUG"
        
        if ($errorOutput) {
            Write-Log "Error output: $errorOutput" "WARN"
        }
        
        return @{
            Success = $process.ExitCode -eq 0
            ExitCode = $process.ExitCode
            Output = $output
            ErrorOutput = $errorOutput
        }
    }
    catch {
        Write-Log "Exception testing command: $($_.Exception.Message)" "ERROR"
        return @{
            Success = $false
            Error = $_.Exception.Message
            Output = ""
            ErrorOutput = ""
        }
    }
    finally {
        if ($process -and -not $process.HasExited) {
            try {
                $process.Kill()
                $process.WaitForExit(5000)
            }
            catch {
                Write-Log "Failed to kill process: $($_.Exception.Message)" "WARN"
            }
        }
        if ($process) {
            $process.Dispose()
        }
    }
}

# Auto-detect server path if not provided
if ([string]::IsNullOrEmpty($ServerPath)) {
    $possiblePaths = @(
        ".\ADPsMCPSvr\Start-ADPsMCPSvr.ps1",
        "C:\ProgramData\Microsoft\AIMX\MCPServers\ADPsMCPSvr\Start-ADPsMCPSvr.ps1",
        ".\Start-ADPsMCPSvr.ps1"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $ServerPath = $path
            break
        }
    }
    
    if ([string]::IsNullOrEmpty($ServerPath)) {
        Write-Log "Could not find ADPsMCPSvr script. Please specify -ServerPath parameter." "ERROR"
        exit 1
    }
}

Write-Log "=== ADPsMCPSvr Test Script Started ===" "INFO"
Write-Log "Server Path: $ServerPath" "INFO"
Write-Log "Timeout: $TimeoutSeconds seconds" "INFO"
Write-Log "Test Basic Only: $TestBasicOnly" "INFO"
Write-Log "Log File: $LogPath" "INFO"
Write-Log "" "INFO"

# Verify server script exists
if (-not (Test-Path $ServerPath)) {
    Write-Log "Server script not found: $ServerPath" "ERROR"
    exit 1
}

# Test cases - start with basic commands
$testCases = @()

# Basic domain information commands
$testCases += @{
    Name = "Get-ADDomain"
    Description = "Get current domain information"
    Parameters = @{}
}

$testCases += @{
    Name = "Get-ADDefaultDomainPasswordPolicy"
    Description = "Get default domain password policy (KNOWN ISSUE)"
    Parameters = @{}
}

$testCases += @{
    Name = "Get-ADForest"
    Description = "Get forest information"
    Parameters = @{}
}

# User management commands
$testCases += @{
    Name = "Get-ADUser"
    Description = "Get current user information"
    Parameters = @{
        Identity = $env:USERNAME
    }
}

if (-not $TestBasicOnly) {
    # Add more complex test cases
    $testCases += @{
        Name = "Get-ADComputer"
        Description = "Get current computer information"
        Parameters = @{
            Identity = $env:COMPUTERNAME
        }
    }
    
    $testCases += @{
        Name = "Get-ADGroup"
        Description = "Get Domain Users group"
        Parameters = @{
            Identity = "Domain Users"
        }
    }
    
    $testCases += @{
        Name = "Get-ADOrganizationalUnit"
        Description = "Get all OUs"
        Parameters = @{
            Filter = "*"
        }
    }
}

# Run tests
$results = @()
$totalTests = $testCases.Count
$passedTests = 0
$failedTests = 0
$timedOutTests = 0

Write-Log "Running $totalTests test cases..." "INFO"
Write-Log "" "INFO"

for ($i = 0; $i -lt $testCases.Count; $i++) {
    $testCase = $testCases[$i]
    $testNumber = $i + 1
    
    Write-Log "[$testNumber/$totalTests] $($testCase.Name): $($testCase.Description)" "INFO"
    
    $result = Test-MCPCommand -CommandName $testCase.Name -Parameters $testCase.Parameters -TimeoutSec $TimeoutSeconds
    
    if ($result.Success) {
        Write-Log "✓ PASSED" "SUCCESS"
        $passedTests++
    }
    elseif ($result.Error -like "*Timeout*") {
        Write-Log "⏱ TIMEOUT" "ERROR"
        $timedOutTests++
    }
    else {
        Write-Log "✗ FAILED: $($result.Error)" "ERROR"
        $failedTests++
    }
    
    $results += @{
        TestCase = $testCase
        Result = $result
        Status = if ($result.Success) { "PASSED" } elseif ($result.Error -like "*Timeout*") { "TIMEOUT" } else { "FAILED" }
    }
    
    Write-Log "" "INFO"
    Start-Sleep -Milliseconds 500  # Brief pause between tests
}

# Summary
$endTime = Get-Date
$duration = $endTime - $StartTime

Write-Log "=== Test Summary ===" "INFO"
Write-Log "Total Tests: $totalTests" "INFO"
Write-Log "Passed: $passedTests" "SUCCESS"
Write-Log "Failed: $failedTests" "ERROR"
Write-Log "Timed Out: $timedOutTests" "ERROR"
Write-Log "Duration: $($duration.TotalSeconds) seconds" "INFO"
Write-Log "" "INFO"

# Detailed results
Write-Log "=== Detailed Results ===" "INFO"
foreach ($result in $results) {
    Write-Log "$($result.TestCase.Name): $($result.Status)" "INFO"
    if ($result.Status -ne "PASSED" -and $result.Result.Error) {
        Write-Log "  Error: $($result.Result.Error)" "ERROR"
    }
    if ($result.Result.ErrorOutput) {
        Write-Log "  Error Output: $($result.Result.ErrorOutput)" "WARN"
    }
}

Write-Log "" "INFO"
Write-Log "Test completed. Check $LogPath for detailed logs." "INFO"

# Exit with appropriate code
if ($failedTests -gt 0 -or $timedOutTests -gt 0) {
    exit 1
}
else {
    exit 0
}
