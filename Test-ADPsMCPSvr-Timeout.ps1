#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Timeout-focused test for ADPsMCPSvr hanging issues
    
.DESCRIPTION
    This script tests the specific timeout and hanging issues with PowerShell
    execution in the MCP server context. It provides detailed diagnostics
    about process behavior and timeout handling.
    
.PARAMETER QuickTest
    Run only quick tests (5 second timeout)
    
.EXAMPLE
    .\Test-ADPsMCPSvr-Timeout.ps1
    
.EXAMPLE
    .\Test-ADPsMCPSvr-Timeout.ps1 -QuickTest
#>

[CmdletBinding()]
param(
    [switch]$QuickTest
)

$timeouts = if ($QuickTest) { @(5, 10) } else { @(5, 10, 15, 30) }

function Write-TestResult {
    param([string]$Message, [string]$Status = "INFO")
    $color = switch ($Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "TIMEOUT" { "Yellow" }
        "INFO" { "Cyan" }
        default { "White" }
    }
    $timestamp = Get-Date -Format "HH:mm:ss.fff"
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

function Test-CommandWithTimeout {
    param(
        [string]$Command,
        [int]$TimeoutSeconds,
        [string]$TestName
    )
    
    Write-TestResult "Testing: $TestName (Timeout: ${TimeoutSeconds}s)" "INFO"
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        # Create a PowerShell runspace for better control
        $runspace = [runspacefactory]::CreateRunspace()
        $runspace.Open()
        
        $powershell = [powershell]::Create()
        $powershell.Runspace = $runspace
        
        # Add the command
        $powershell.AddScript($Command) | Out-Null
        
        # Start execution
        $asyncResult = $powershell.BeginInvoke()
        
        # Wait with timeout
        $completed = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutSeconds * 1000)
        
        $stopwatch.Stop()
        $elapsed = $stopwatch.Elapsed.TotalSeconds
        
        if ($completed) {
            try {
                $result = $powershell.EndInvoke($asyncResult)
                $errors = $powershell.Streams.Error
                
                if ($errors.Count -gt 0) {
                    Write-TestResult "FAIL - Errors: $($errors[0].Exception.Message) (${elapsed:F2}s)" "FAIL"
                    return "FAIL"
                } else {
                    Write-TestResult "PASS - Completed successfully (${elapsed:F2}s)" "PASS"
                    return "PASS"
                }
            }
            catch {
                Write-TestResult "FAIL - Exception: $($_.Exception.Message) (${elapsed:F2}s)" "FAIL"
                return "FAIL"
            }
        } else {
            Write-TestResult "TIMEOUT - Command hung (${elapsed:F2}s)" "TIMEOUT"
            
            # Try to stop the runspace
            try {
                $powershell.Stop()
                $runspace.Close()
            }
            catch {
                Write-TestResult "Warning: Could not stop runspace cleanly" "INFO"
            }
            
            return "TIMEOUT"
        }
    }
    catch {
        $stopwatch.Stop()
        Write-TestResult "FAIL - Setup exception: $($_.Exception.Message) ($($stopwatch.Elapsed.TotalSeconds):F2s)" "FAIL"
        return "FAIL"
    }
    finally {
        if ($powershell) { $powershell.Dispose() }
        if ($runspace) { $runspace.Dispose() }
    }
}

function Test-ProcessBasedExecution {
    param(
        [string]$Command,
        [int]$TimeoutSeconds,
        [string]$TestName
    )
    
    Write-TestResult "Testing (Process): $TestName (Timeout: ${TimeoutSeconds}s)" "INFO"
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $psi = New-Object System.Diagnostics.ProcessStartInfo
        $psi.FileName = "powershell.exe"
        $psi.Arguments = "-ExecutionPolicy Bypass -NoProfile -Command `"$Command`""
        $psi.UseShellExecute = $false
        $psi.RedirectStandardOutput = $true
        $psi.RedirectStandardError = $true
        $psi.CreateNoWindow = $true
        
        $process = [System.Diagnostics.Process]::Start($psi)
        
        $completed = $process.WaitForExit($TimeoutSeconds * 1000)
        $stopwatch.Stop()
        $elapsed = $stopwatch.Elapsed.TotalSeconds
        
        if ($completed) {
            $output = $process.StandardOutput.ReadToEnd()
            $error = $process.StandardError.ReadToEnd()
            
            if ($process.ExitCode -eq 0) {
                Write-TestResult "PASS - Process completed (${elapsed:F2}s)" "PASS"
                return "PASS"
            } else {
                Write-TestResult "FAIL - Exit code: $($process.ExitCode), Error: $error (${elapsed:F2}s)" "FAIL"
                return "FAIL"
            }
        } else {
            Write-TestResult "TIMEOUT - Process hung, killing (${elapsed:F2}s)" "TIMEOUT"
            try {
                $process.Kill()
                $process.WaitForExit(5000)
            }
            catch {
                Write-TestResult "Warning: Could not kill process cleanly" "INFO"
            }
            return "TIMEOUT"
        }
    }
    catch {
        $stopwatch.Stop()
        Write-TestResult "FAIL - Process exception: $($_.Exception.Message) ($($stopwatch.Elapsed.TotalSeconds):F2s)" "FAIL"
        return "FAIL"
    }
    finally {
        if ($process -and -not $process.HasExited) {
            try { $process.Kill() } catch { }
        }
        if ($process) { $process.Dispose() }
    }
}

# Test commands
$testCommands = @(
    @{
        Name = "Simple Test"
        Command = "Write-Output 'Hello World'"
    },
    @{
        Name = "AD Module Import"
        Command = "Import-Module ActiveDirectory; Write-Output 'AD Module Loaded'"
    },
    @{
        Name = "Get-ADDomain"
        Command = "Import-Module ActiveDirectory; Get-ADDomain | Select-Object Name, DomainMode"
    },
    @{
        Name = "Get-ADDefaultDomainPasswordPolicy (PROBLEM COMMAND)"
        Command = "Import-Module ActiveDirectory; Get-ADDefaultDomainPasswordPolicy"
    },
    @{
        Name = "Get-ADDefaultDomainPasswordPolicy with -Current"
        Command = "Import-Module ActiveDirectory; Get-ADDefaultDomainPasswordPolicy -Current LocalComputer"
    }
)

Write-TestResult "=== ADPsMCPSvr Timeout Test Suite ===" "INFO"
Write-TestResult "Testing with timeouts: $($timeouts -join ', ') seconds" "INFO"
Write-TestResult "Quick Test Mode: $QuickTest" "INFO"
Write-TestResult "" "INFO"

$results = @()

foreach ($timeout in $timeouts) {
    Write-TestResult "=== Testing with ${timeout}s timeout ===" "INFO"
    
    foreach ($testCmd in $testCommands) {
        Write-TestResult "" "INFO"
        
        # Test with runspace
        $runspaceResult = Test-CommandWithTimeout -Command $testCmd.Command -TimeoutSeconds $timeout -TestName "$($testCmd.Name) [Runspace]"
        
        # Test with process
        $processResult = Test-ProcessBasedExecution -Command $testCmd.Command -TimeoutSeconds $timeout -TestName "$($testCmd.Name) [Process]"
        
        $results += @{
            Command = $testCmd.Name
            Timeout = $timeout
            RunspaceResult = $runspaceResult
            ProcessResult = $processResult
        }
        
        Start-Sleep -Milliseconds 500
    }
    
    Write-TestResult "" "INFO"
}

# Summary
Write-TestResult "=== Test Results Summary ===" "INFO"
Write-TestResult "" "INFO"

$groupedResults = $results | Group-Object Command

foreach ($group in $groupedResults) {
    Write-TestResult "Command: $($group.Name)" "INFO"
    
    foreach ($result in $group.Group) {
        $runspaceStatus = $result.RunspaceResult
        $processStatus = $result.ProcessResult
        $timeout = $result.Timeout
        
        $runspaceColor = switch ($runspaceStatus) { "PASS" { "Green" } "TIMEOUT" { "Yellow" } default { "Red" } }
        $processColor = switch ($processStatus) { "PASS" { "Green" } "TIMEOUT" { "Yellow" } default { "Red" } }
        
        Write-Host "  ${timeout}s: Runspace=" -NoNewline
        Write-Host $runspaceStatus -ForegroundColor $runspaceColor -NoNewline
        Write-Host ", Process=" -NoNewline
        Write-Host $processStatus -ForegroundColor $processColor
    }
    Write-TestResult "" "INFO"
}

# Analysis
Write-TestResult "=== Analysis ===" "INFO"

$problemCommand = $results | Where-Object { $_.Command -like "*PROBLEM COMMAND*" }
$timeoutCount = ($problemCommand | Where-Object { $_.RunspaceResult -eq "TIMEOUT" -or $_.ProcessResult -eq "TIMEOUT" }).Count

if ($timeoutCount -gt 0) {
    Write-TestResult "❌ CONFIRMED: Get-ADDefaultDomainPasswordPolicy hangs in $timeoutCount out of $($problemCommand.Count) tests" "FAIL"
    Write-TestResult "This confirms the hanging issue reported in the logs" "INFO"
} else {
    Write-TestResult "✅ UNEXPECTED: Get-ADDefaultDomainPasswordPolicy did not hang in any tests" "PASS"
    Write-TestResult "The issue may be environment-specific or intermittent" "INFO"
}

$adDomainResults = $results | Where-Object { $_.Command -eq "Get-ADDomain" }
$adDomainPassed = ($adDomainResults | Where-Object { $_.RunspaceResult -eq "PASS" -and $_.ProcessResult -eq "PASS" }).Count

if ($adDomainPassed -gt 0) {
    Write-TestResult "✅ Get-ADDomain works correctly - AD connectivity is OK" "PASS"
} else {
    Write-TestResult "❌ Get-ADDomain also fails - may be AD connectivity issue" "FAIL"
}

Write-TestResult "" "INFO"
Write-TestResult "Test completed. Use these results to troubleshoot the hanging issue." "INFO"

# Exit code based on whether we confirmed the problem
if ($timeoutCount -gt 0) {
    exit 1  # Problem confirmed
} else {
    exit 0  # No problem detected
}
