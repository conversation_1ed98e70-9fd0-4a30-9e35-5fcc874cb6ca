using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace netRag;

/// <summary>
/// API controller for managing MCP servers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class McpServersController : ControllerBase
{
    private readonly McpServerService _serverService;
    private readonly ILogger<McpServersController> _logger;

    public McpServersController(McpServerService serverService, ILogger<McpServersController> logger)
    {
        _serverService = serverService;
        _logger = logger;
    }

    /// <summary>
    /// Register a new MCP server
    /// </summary>
    /// <param name="request">Server registration request</param>
    /// <returns>Success status</returns>
    [HttpPost("register")]
    public async Task<IActionResult> RegisterServer([FromBody] RegisterServerRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(request.Name))
            {
                return BadRequest(new { error = "Server ID and Name are required" });
            }

            var success = await _serverService.RegisterServerAsync(request);
            
            if (success)
            {
                _logger.LogInformation("Server registered successfully: {ServerId}", request.Id);
                return Ok(new { success = true, message = "Server registered successfully", serverId = request.Id });
            }
            else
            {
                return BadRequest(new { error = "Failed to register server. Check server ID format (GUID)" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering server: {ServerId}", request.Id);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get all registered MCP servers
    /// </summary>
    /// <returns>List of all MCP servers</returns>
    [HttpGet]
    public async Task<IActionResult> GetAllServers()
    {
        try
        {
            var servers = await _serverService.GetAllServersAsync();
            return Ok(new { servers, count = servers.Count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all servers");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get a specific MCP server by ID
    /// </summary>
    /// <param name="serverId">Server ID (GUID)</param>
    /// <returns>Server details</returns>
    [HttpGet("{serverId}")]
    public async Task<IActionResult> GetServer(string serverId)
    {
        try
        {
            if (string.IsNullOrEmpty(serverId))
            {
                return BadRequest(new { error = "Server ID is required" });
            }

            var server = await _serverService.GetServerAsync(serverId);
            
            if (server == null)
            {
                return NotFound(new { error = "Server not found", serverId });
            }

            return Ok(server);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server: {ServerId}", serverId);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Remove a MCP server
    /// </summary>
    /// <param name="serverId">Server ID (GUID)</param>
    /// <returns>Success status</returns>
    [HttpDelete("{serverId}")]
    public async Task<IActionResult> RemoveServer(string serverId)
    {
        try
        {
            if (string.IsNullOrEmpty(serverId))
            {
                return BadRequest(new { error = "Server ID is required" });
            }

            var removed = await _serverService.RemoveServerAsync(serverId);
            
            if (removed)
            {
                return Ok(new { success = true, message = "Server removed successfully", serverId });
            }
            else
            {
                return NotFound(new { error = "Server not found", serverId });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing server: {ServerId}", serverId);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get server statistics
    /// </summary>
    /// <returns>Statistics about all servers</returns>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetServerStatistics()
    {
        try
        {
            var stats = await _serverService.GetServerStatisticsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting server statistics");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Check if a server exists
    /// </summary>
    /// <param name="serverId">Server ID (GUID)</param>
    /// <returns>Existence status</returns>
    [HttpGet("{serverId}/exists")]
    public async Task<IActionResult> ServerExists(string serverId)
    {
        try
        {
            if (string.IsNullOrEmpty(serverId))
            {
                return BadRequest(new { error = "Server ID is required" });
            }

            var exists = await _serverService.ServerExistsAsync(serverId);
            return Ok(new { serverId, exists });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking server existence: {ServerId}", serverId);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }
}
