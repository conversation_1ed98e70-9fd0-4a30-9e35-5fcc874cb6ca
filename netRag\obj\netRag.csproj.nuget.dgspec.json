{"format": 1, "restore": {"F:\\Code\\724\\netRag\\netRag.csproj": {}}, "projects": {"F:\\Code\\724\\netRag\\netRag.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Code\\724\\netRag\\netRag.csproj", "projectName": "netRag", "projectPath": "F:\\Code\\724\\netRag\\netRag.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Code\\724\\netRag\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging.EventLog": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.6, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}