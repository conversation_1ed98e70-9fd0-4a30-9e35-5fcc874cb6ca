#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Simple test script for ADPsMCPSvr hanging issue
    
.DESCRIPTION
    This script specifically tests the Get-ADDefaultDomainPasswordPolicy command
    that is known to hang. It includes multiple test approaches to isolate the issue.
    
.PARAMETER TimeoutSeconds
    Timeout for each test in seconds (default: 15)
    
.EXAMPLE
    .\Test-ADPsMCPSvr-Simple.ps1
    
.EXAMPLE
    .\Test-ADPsMCPSvr-Simple.ps1 -TimeoutSeconds 30
#>

[CmdletBinding()]
param(
    [int]$TimeoutSeconds = 15
)

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "TEST" { "Cyan" }
        default { "White" }
    }
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

function Test-PowerShellCommand {
    param(
        [string]$Command,
        [int]$TimeoutSec = 15,
        [string]$Description = ""
    )
    
    Write-TestLog "Testing: $Description" "TEST"
    Write-TestLog "Command: $Command" "INFO"
    
    try {
        $job = Start-Job -ScriptBlock {
            param($cmd)
            try {
                Import-Module ActiveDirectory -ErrorAction Stop
                Invoke-Expression $cmd
            }
            catch {
                throw $_.Exception.Message
            }
        } -ArgumentList $Command
        
        $completed = Wait-Job $job -Timeout $TimeoutSec
        
        if ($completed) {
            $result = Receive-Job $job
            $error = $job.Error
            Remove-Job $job
            
            if ($error) {
                Write-TestLog "✗ FAILED: $error" "ERROR"
                return $false
            } else {
                Write-TestLog "✓ SUCCESS: Command completed" "SUCCESS"
                if ($result) {
                    Write-TestLog "Result: $($result | Out-String -Width 100)" "INFO"
                }
                return $true
            }
        } else {
            Write-TestLog "⏱ TIMEOUT: Command hung after $TimeoutSec seconds" "ERROR"
            Remove-Job $job -Force
            return $false
        }
    }
    catch {
        Write-TestLog "✗ EXCEPTION: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-ADModuleDirectly {
    Write-TestLog "=== Testing AD Module Directly ===" "TEST"
    
    # Test 1: Basic AD module import
    $success1 = Test-PowerShellCommand -Command "Get-Module ActiveDirectory" -Description "Check if AD module is loaded" -TimeoutSec 5
    
    # Test 2: Import AD module
    $success2 = Test-PowerShellCommand -Command "Import-Module ActiveDirectory" -Description "Import AD module" -TimeoutSec 10
    
    # Test 3: Simple AD command
    $success3 = Test-PowerShellCommand -Command "Get-ADDomain" -Description "Get current domain" -TimeoutSec $TimeoutSeconds
    
    # Test 4: The problematic command
    $success4 = Test-PowerShellCommand -Command "Get-ADDefaultDomainPasswordPolicy" -Description "Get default password policy (KNOWN ISSUE)" -TimeoutSec $TimeoutSeconds
    
    # Test 5: Alternative approach with specific domain
    $success5 = Test-PowerShellCommand -Command "Get-ADDefaultDomainPasswordPolicy -Current LocalComputer" -Description "Get password policy with -Current parameter" -TimeoutSec $TimeoutSeconds
    
    return @($success1, $success2, $success3, $success4, $success5)
}

function Test-MCPServerDirect {
    Write-TestLog "=== Testing MCP Server Process ===" "TEST"
    
    # Find the server script
    $serverPaths = @(
        ".\ADPsMCPSvr\Start-ADPsMCPSvr.ps1",
        "C:\ProgramData\Microsoft\AIMX\MCPServers\ADPsMCPSvr\Start-ADPsMCPSvr.ps1",
        ".\Start-ADPsMCPSvr.ps1"
    )
    
    $serverPath = $null
    foreach ($path in $serverPaths) {
        if (Test-Path $path) {
            $serverPath = $path
            break
        }
    }
    
    if (-not $serverPath) {
        Write-TestLog "Could not find ADPsMCPSvr script" "ERROR"
        return $false
    }
    
    Write-TestLog "Found server script: $serverPath" "INFO"
    
    try {
        # Start the MCP server process
        $psi = New-Object System.Diagnostics.ProcessStartInfo
        $psi.FileName = "powershell.exe"
        $psi.Arguments = "-ExecutionPolicy Bypass -NoProfile -File `"$serverPath`""
        $psi.UseShellExecute = $false
        $psi.RedirectStandardInput = $true
        $psi.RedirectStandardOutput = $true
        $psi.RedirectStandardError = $true
        $psi.CreateNoWindow = $true
        
        $process = [System.Diagnostics.Process]::Start($psi)
        
        Write-TestLog "Started MCP server process (PID: $($process.Id))" "INFO"
        
        # Send initialization
        $initRequest = @{
            jsonrpc = "2.0"
            id = 1
            method = "initialize"
            params = @{
                protocolVersion = "2024-11-05"
                capabilities = @{ tools = @{} }
                clientInfo = @{ name = "TestClient"; version = "1.0.0" }
            }
        } | ConvertTo-Json -Depth 5 -Compress
        
        $process.StandardInput.WriteLine($initRequest)
        $process.StandardInput.Flush()
        
        # Wait for initialization response
        Start-Sleep -Seconds 2
        
        # Send the problematic command
        $toolRequest = @{
            jsonrpc = "2.0"
            id = 2
            method = "tools/call"
            params = @{
                name = "Get-ADDefaultDomainPasswordPolicy"
                arguments = @{}
            }
        } | ConvertTo-Json -Depth 5 -Compress
        
        Write-TestLog "Sending tool request: Get-ADDefaultDomainPasswordPolicy" "INFO"
        $process.StandardInput.WriteLine($toolRequest)
        $process.StandardInput.Flush()
        
        # Wait for response with timeout
        $completed = $process.WaitForExit($TimeoutSeconds * 1000)
        
        if ($completed) {
            $output = $process.StandardOutput.ReadToEnd()
            $error = $process.StandardError.ReadToEnd()
            
            Write-TestLog "✓ MCP server completed" "SUCCESS"
            Write-TestLog "Output: $output" "INFO"
            if ($error) {
                Write-TestLog "Error: $error" "WARN"
            }
            return $true
        } else {
            Write-TestLog "⏱ MCP server hung - killing process" "ERROR"
            $process.Kill()
            $process.WaitForExit(5000)
            return $false
        }
    }
    catch {
        Write-TestLog "✗ Exception testing MCP server: $($_.Exception.Message)" "ERROR"
        return $false
    }
    finally {
        if ($process -and -not $process.HasExited) {
            try { $process.Kill() } catch { }
        }
        if ($process) { $process.Dispose() }
    }
}

function Test-EnvironmentInfo {
    Write-TestLog "=== Environment Information ===" "TEST"
    
    Write-TestLog "PowerShell Version: $($PSVersionTable.PSVersion)" "INFO"
    Write-TestLog "OS Version: $([System.Environment]::OSVersion.VersionString)" "INFO"
    Write-TestLog "Computer Name: $env:COMPUTERNAME" "INFO"
    Write-TestLog "Domain: $env:USERDOMAIN" "INFO"
    Write-TestLog "User: $env:USERNAME" "INFO"
    
    # Check if domain-joined
    try {
        $domain = [System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()
        Write-TestLog "Current Domain: $($domain.Name)" "INFO"
        Write-TestLog "Domain Controllers: $($domain.DomainControllers.Count)" "INFO"
    }
    catch {
        Write-TestLog "Not domain-joined or AD access issue: $($_.Exception.Message)" "WARN"
    }
    
    # Check AD module
    $adModule = Get-Module ActiveDirectory -ListAvailable
    if ($adModule) {
        Write-TestLog "AD Module Available: $($adModule.Version)" "INFO"
    } else {
        Write-TestLog "AD Module NOT Available" "ERROR"
    }
}

# Main execution
Write-TestLog "=== ADPsMCPSvr Hanging Issue Test ===" "TEST"
Write-TestLog "Timeout: $TimeoutSeconds seconds" "INFO"
Write-TestLog "" "INFO"

# Test environment
Test-EnvironmentInfo
Write-TestLog "" "INFO"

# Test AD commands directly
$directResults = Test-ADModuleDirectly
Write-TestLog "" "INFO"

# Test MCP server
$mcpResult = Test-MCPServerDirect
Write-TestLog "" "INFO"

# Summary
Write-TestLog "=== Test Summary ===" "TEST"
Write-TestLog "AD Module Load: $(if ($directResults[0]) { 'PASS' } else { 'FAIL' })" "INFO"
Write-TestLog "AD Module Import: $(if ($directResults[1]) { 'PASS' } else { 'FAIL' })" "INFO"
Write-TestLog "Get-ADDomain: $(if ($directResults[2]) { 'PASS' } else { 'FAIL' })" "INFO"
Write-TestLog "Get-ADDefaultDomainPasswordPolicy: $(if ($directResults[3]) { 'PASS' } else { 'FAIL' })" "INFO"
Write-TestLog "Get-ADDefaultDomainPasswordPolicy -Current: $(if ($directResults[4]) { 'PASS' } else { 'FAIL' })" "INFO"
Write-TestLog "MCP Server Test: $(if ($mcpResult) { 'PASS' } else { 'FAIL' })" "INFO"

$totalTests = $directResults.Count + 1
$passedTests = ($directResults | Where-Object { $_ }).Count + $(if ($mcpResult) { 1 } else { 0 })

Write-TestLog "" "INFO"
Write-TestLog "Overall: $passedTests/$totalTests tests passed" "INFO"

if ($passedTests -lt $totalTests) {
    Write-TestLog "Some tests failed - check the output above for details" "WARN"
    exit 1
} else {
    Write-TestLog "All tests passed!" "SUCCESS"
    exit 0
}
