using System.Text.Json.Serialization;

namespace netRag;

/// <summary>
/// Represents an MCP tool with its metadata and schema information
/// </summary>
public class McpTool
{
    /// <summary>
    /// Unique identifier in format: ServerGUID_ToolGUID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// GUID of the MCP server that provides this tool
    /// </summary>
    public string ServerGuid { get; set; } = string.Empty;

    /// <summary>
    /// GUID of the specific tool within the server
    /// </summary>
    public string ToolGuid { get; set; } = string.Empty;

    /// <summary>
    /// Name of the tool (e.g., "Get-ADUser")
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of what the tool does
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Category or domain of the tool (e.g., "FileSystem", "Database", "Network")
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// JSON schema for the tool's input parameters
    /// </summary>
    public string InputSchema { get; set; } = string.Empty;

    /// <summary>
    /// Parsed input parameters for easier access
    /// </summary>
    public List<McpToolParameter> Parameters { get; set; } = new();

    /// <summary>
    /// Usage examples or sample calls
    /// </summary>
    public List<string> Examples { get; set; } = new();

    /// <summary>
    /// Tags for additional categorization and search
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// When this tool was registered
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this tool was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Version of the tool
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// Additional metadata as key-value pairs
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Represents a parameter of an MCP tool
/// </summary>
public class McpToolParameter
{
    /// <summary>
    /// Parameter name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Parameter type (string, integer, boolean, array, object, etc.)
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Parameter description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether this parameter is required
    /// </summary>
    public bool Required { get; set; } = false;

    /// <summary>
    /// Default value if any
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// Allowed values for enum types
    /// </summary>
    public List<string> EnumValues { get; set; } = new();

    /// <summary>
    /// Additional parameter constraints or metadata
    /// </summary>
    public Dictionary<string, object> Constraints { get; set; } = new();
}

/// <summary>
/// Request model for registering a new MCP tool
/// </summary>
public class RegisterToolRequest
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string InputSchema { get; set; } = string.Empty;
    public List<string> Examples { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string Version { get; set; } = "1.0.0";
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Request model for searching MCP tools
/// </summary>
public class SearchToolsRequest
{
    /// <summary>
    /// Search query describing what the user wants to do
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// Maximum number of tools to return (default: 10)
    /// </summary>
    public int TopK { get; set; } = 10;

    /// <summary>
    /// Optional category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Optional tags filter
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Minimum similarity score threshold (0.0 to 1.0)
    /// </summary>
    public float MinScore { get; set; } = 0.0f;
}

/// <summary>
/// Response model for tool search results
/// </summary>
public class SearchToolsResponse
{
    /// <summary>
    /// The original search query
    /// </summary>
    public string Query { get; set; } = string.Empty;

    /// <summary>
    /// List of matching tools with relevance scores
    /// </summary>
    public List<ToolSearchResult> Tools { get; set; } = new();

    /// <summary>
    /// Total number of tools searched
    /// </summary>
    public int TotalSearched { get; set; }

    /// <summary>
    /// Time taken for the search in milliseconds
    /// </summary>
    public long SearchTimeMs { get; set; }
}

/// <summary>
/// Individual tool search result with relevance score
/// </summary>
public class ToolSearchResult
{
    /// <summary>
    /// The MCP tool
    /// </summary>
    public McpTool Tool { get; set; } = new();

    /// <summary>
    /// Relevance score (0.0 to 1.0, higher is more relevant)
    /// </summary>
    public float Score { get; set; }

    /// <summary>
    /// Explanation of why this tool was matched (optional)
    /// </summary>
    public string? MatchReason { get; set; }
}

/// <summary>
/// Represents an MCP server that provides tools
/// </summary>
public class McpServer
{
    /// <summary>
    /// Unique identifier for the MCP server
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Display name of the MCP server
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what this MCP server provides
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Version of the MCP server
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// When this server was registered
    /// </summary>
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Number of tools provided by this server
    /// </summary>
    public int ToolCount { get; set; }

    /// <summary>
    /// Additional server metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Request to register a new MCP server
/// </summary>
public class RegisterServerRequest
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0.0";
    public Dictionary<string, object> Metadata { get; set; } = new();
}
